/**
 * Fullscreen modal styles 
 */
#btd-fullscreen-portal-root {
  background-color: var(--btd-overlay-background);
  color: white;
  position: fixed;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 9999;
}

#btd-fullscreen-portal-root.open {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/**
 * Override TweetDeck's modal styles.
 */

:root {
  --btd-overlay-background: rgba(0, 0, 0, 0.88);
  --btd-original-overlay-background: rgba(0, 0, 0, 0.88);
}

html .btd-loaded .overlay,
html .btd-loaded .ovl {
  background-color: var(--btd-overlay-background);
  transition-property: background-color;
  transition-duration: 0.5s;
}

.js-modal-panel.mdl.s-full.med-fullpanel {
  background: transparent;
  pointer-events: none;
  box-shadow: none;
}

.js-mediatable .js-modal-panel .js-mediaembed,
.med-tweet,
.mdl-btn-media {
  pointer-events: all;
}

.mdl-media-prev,
.mdl-media-next {
  background: transparent !important;
  z-index: 10;

  & .icon::after {
    content: '';
    position: absolute;
    background: rgba(21, 21, 21, 0.6);
    border-radius: 50%;
    width: 42px;
    height: 42px;
    top: 0;
    left: 0;
    z-index: 0;
  }

  & .icon::before {
    position: absolute;
    left: 45%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
}

.mdl-media-next .icon::after {
  transform: translate(-22%, -20%);
}

.mdl-media-prev .icon::after {
  transform: translate(-16%, -20%);
}

/**
 * Some fixes around default dark theme
 */

html.dark .lst-modal {
  background: #15202b;
  border: 1px solid #38444d;
}

html.dark .typeahead .fullname,
html.dark .typeahead .username,
html.dark .typeahead .js-hashtag {
  color: white;
}

/* Small UI tweaks */
.app-columns {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 0 !important;
}

.app-columns-container {
  bottom: 0 !important;
}

.column {
  margin-right: 2px !important;
}

.js-media.media-grid-container + p.txt-ellipsis {
  display: none;
}

.app-navigator > a.js-header-action:not(.is-hidden) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
}

.app-navigator > a.js-header-action:not(.is-hidden) > .padding-ts {
  padding-top: 0 !important;
}

/* Prevent horizontal scrollbar in columns inside modals */
.js-modal.open-modal div.js-right-column.scroll-v {
  overflow-x: hidden;
}

.column-navigator {
  bottom: 280px;
}

.column-navigator-overflow {
  bottom: 300px;
}

.icon-user-check:before {
  content: '\F105';
}
.icon-earth:before {
  content: '\F102';
}

#settings-modal .mdl-content {
  display: flex;
}

#settings-modal > .mdl {
  width: 800px;
  height: 600px;
}

@media (max-width: 810px) {
  #settings-modal > .mdl {
    width: 90vh;
  }
}

@media (max-height: 620px) {
  #settings-modal > .mdl {
    height: 90vh;
  }
}

#settings-modal .mdl-content > .mdl-column-lrg {
  width: auto !important;
  flex: 1;
}

#settings-modal .mdl-content #general_settings .control-group .divider-bar + div {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: auto;
}

html body {
  background-color: #1c2938;
}
html.dark body {
  background-color: #15202b;
}

.btd-profile-label:not(.pronouns) {
  flex-basis: 100%;
}

.tweet .btd-profile-label:hover {
  color: #8899a6 !important;
}

.btd-profile-label img {
  margin-right: 2px;
  height: 15px;
  width: 15px;
}

.account-summary-text .btd-profile-label {
  display: inline-block;
  margin-top: 4px;
}

.prf-header .btd-profile-label img {
  filter: brightness(100) drop-shadow(0 1px 1px rgba(20, 23, 26, 0.8));
}

.btd-profile-label.pronouns {
  flex-shrink: 0;
}

.btd-profile-label.pronouns + .other-replies {
  text-transform: lowercase;
}

.btd-profile-label.pronouns + .other-replies::before {
  content: '\00A0\00B7';
}

.btd-profile-label.pronouns + .other-replies > a {
  text-transform: none;
}

.pronouns-wrapper + .pronouns-wrapper {
  display: none !important;
}

.thread + .pronouns-wrapper {
  margin-top: 3px;
}

i.icon-btd {
  background-image: url('../assets/btd-logo-small.png');
  background-size: 100%;
}

section[data-column-size='small'] .hw-card-container {
  width: 50% !important;
}

section[data-column-size='small'] .hw-card-container div[style*='-webkit-line-clamp'] {
  -webkit-line-clamp: 1 !important;
}

.js-drawer .js-compose-text.compose-text[style*='height: 130px'] {
  height: 200px !important;
}
.js-drawer .js-compose-text.compose-text[style*='height: 180px'] {
  height: 250px !important;
}

/** TD Preview stuff */
.btd-settings-header {
  grid-area: header;
  padding: 15px;
  font-size: 20px;
  text-align: left;
  border-bottom: 1px solid var(--settings-modal-separator);

  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: auto;
  grid-column-gap: 10px;
  justify-content: flex-start;
  align-items: center;
}
#btd-rollback-dialog-root {
  --settings-modal-background: #151f2a;
  --settings-modal-menu-item-selected: white;
  --settings-modal-menu-item-hover: white;
  --settings-modal-menu-item-hover-fg: white;
  --settings-modal-menu-item: rgba(255, 255, 255, 0.7);
  --settings-modal-text: white;
  --settings-modal-muted-text: #8899a6;
  --settings-modal-separator: rgb(0, 0, 0);
  --twitter-input-bg: #0d1118;
  --twitter-input-placeholder: #8899a6;
  --twitter-input-border-color: #14171a;
  --twitter-blue: #1da1f2;
  --twitter-darkblue: #005fd1;

  font-family: system-ui;
  background-color: var(--btd-overlay-background);
  backdrop-filter: blur(10px);
  color: white;
  position: fixed;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 9999;
  display: grid;
  place-items: center;
  inset: 0;
}

@media screen and (prefers-color-scheme: light) {
  #btd-rollback-dialog-root {
    --settings-modal-background: #ffffff;
    --settings-modal-menu-item-selected: #1da1f2;
    --settings-modal-menu-item-hover: #1170aa;
    --settings-modal-menu-item: #14171a;
    --settings-modal-text: #14171a;
    --settings-modal-separator: #ccd6dd;
    --twitter-input-bg: white;
    --twitter-input-placeholder: #8899a6;
    --twitter-input-border-color: #e1e8ed;
  }
}

#btd-rollback-dialog-root h3 {
  line-height: 1.7;
  margin: 0;
  margin-top: 10px;
  font-size: 20px;

  padding: 6px 0;
  text-align: left;
  display: flex;
  align-items: center;
}

#btd-rollback-dialog-root .btd-dialog {
  color: var(--settings-modal-text);
  background-color: var(--settings-modal-background);

  width: 450px;
  height: auto;
  border-radius: 10px;

  & .buttons {
    display: flex;
    justify-items: flex-end;
    align-items: center;
    justify-content: flex-end;
    gap: 14px;
    padding: 10px;
    margin: 0 -20px;
  }

  & p {
    line-height: 1.6;

    & :any-link {
      color: var(--twitter-blue);
      text-decoration: underline;
    }
  }
}

.btd-settings-button {
  border-radius: 100px;
  font-size: 14px;
  line-height: 21px;
  font-weight: bold;
  outline: none;
  border: 1px solid;
  padding: 3px 12px;
  cursor: pointer;
  text-decoration: none;
}

.btd-settings-button.primary {
  color: white;
  background-color: var(--twitter-blue);
  border-color: var(--twitter-blue);

  &:hover {
    background-color: var(--twitter-darkblue);
    border-color: var(--twitter-darkblue);
  }

  &[disabled],
  &.disabled {
    opacity: 0.7;
    pointer-events: none;
  }
}

.btd-settings-button.secondary {
  color: var(--twitter-blue);
  background-color: transparent;
  border-color: var(--twitter-blue);

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
}
