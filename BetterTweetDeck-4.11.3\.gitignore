.DS_Store
node_modules
dist/
web-ext-artifacts/
build/
artifacts/*
config/*.json
!config/default.json

# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio and Webstorm
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839
# From: https://github.com/github/gitignore/blob/master/Global/JetBrains.gitignore
# User-specific stuff:
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/dictionaries/*
# Sensitive or high-churn files:
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.xml
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml

*.log
*.xcuserstate
*.xcworkspacedata
xcuserdata
.yarn
