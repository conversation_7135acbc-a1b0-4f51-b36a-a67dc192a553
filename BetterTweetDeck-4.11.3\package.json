{"name": "bettertweetdeck", "version": "4.11.3", "description": "Adds some nice options on TweetDeck.", "repository": {"type": "git", "url": "https://github.com/eramdam/BetterTweetDeck.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/eramdam/BetterTweetDeck/issues"}, "scripts": {"_start": "cross-env NODE_ENV=development webpack --watch --progress --env", "start:firefox": "npm run _start -- browser=firefox", "start:firefox-beta": "npm run _start -- browser=firefox-beta", "start:safari": "npm run _start -- browser=safari", "start:chrome": "npm run _start -- browser=chrome", "_build": "cross-env NODE_ENV=production webpack --progress --env", "build:safari": "npm run _build -- browser=safari", "build:firefox": "npm run _build -- browser=firefox", "build:firefox-beta": "npm run _build -- browser=firefox-beta", "build:chrome": "npm run _build -- browser=chrome", "_build:prod": "cross-env NODE_ENV=production webpack --progress --env", "build:prod:safari": "npm run _build:prod -- browser=safari", "build:prod:firefox": "npm run _build:prod -- browser=firefox", "build:prod:firefox-beta": "npm run _build:prod -- browser=firefox-beta", "build:prod:chrome": "npm run _build:prod -- browser=chrome", "fix": "prettier --write . && eslint --fix . --ext .jsx,.js,.ts,.tsx", "lint": "eslint . --ext .jsx,.js,.ts,.tsx", "typecheck:watch": "tsc -p . --watch", "ts-node": "ts-node", "typecheck": "tsc -p .", "postinstall": "ts-node tools/createDefineConfig.ts", "run:firefox": "web-ext run -s dist", "run:chrome": "web-ext run -s dist --target=chromium", "test": "npm run lint && npm run typecheck && jest", "jest": "jest", "release": "rimraf artifacts/* && ts-node tools/release.ts", "update-xcode": "node tools/update_safari_files.js", "pack:safari": "npm run update-xcode && xcodebuild -project 'safari/Better TweetDeck for Safari/BetterTDeck for TweetDeck.xcodeproj/'", "prepare": "husky install"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint", "*.{css,md,html,json,js,jsx,ts,tsx}": "prettier --write"}, "browserslist": ["last 3 chrome versions", "firefox esr", "last 3 edge versions", "last 2 safari versions"], "homepage": "https://github.com/eramdam/BetterTweetDeck", "dependencies": {"@emotion/css": "^11.10.0", "@floating-ui/dom": "^1.0.1", "@floating-ui/react-dom": "^1.0.0", "@octokit/core": "^4.0.5", "@types/archiver": "^5.3.1", "@types/color-convert": "^2.0.0", "@types/config": "^3.3.0", "@types/emoji-mart": "^3.0.9", "@types/file-saver": "^2.0.5", "@types/glob": "^7.2.0", "@types/he": "^1.1.2", "@types/hogan.js": "^3.0.1", "@types/jquery": "^3.5.14", "@types/lodash": "^4.14.184", "@types/luxon": "^3.0.0", "@types/node": "^18.7.9", "@types/prettier": "^2.7.0", "@types/react": "^18.0.17", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.0.6", "@types/semver": "^7.3.12", "@types/stylis": "^4.0.2", "@types/twemoji-parser": "^13.1.1", "@types/twit": "^2.2.30", "@types/webextension-polyfill": "^0.9.0", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "archiver": "^5.3.1", "axios": "^0.27.2", "chalk": "^4.*.*", "color-convert": "^2.0.1", "config": "^4.1.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "discord.js": "^14.3.0", "emoji-mart": "^3.0.1", "emojilib": "^3.0.7", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^2.0.0", "execa": "^5.*.*", "file-saver": "^2.0.5", "fp-ts": "^2.12.2", "framer-motion": "^7.2.0", "fuse.js": "^6.6.2", "generate-json-webpack-plugin": "^2.0.0", "glob": "^8.0.3", "he": "^1.2.0", "hogan.js": "^3.0.2", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.1", "io-ts": "^2.2.17", "io-ts-reporters": "^2.0.1", "json-to-ts": "^1.7.0", "lint-staged": "^13.0.3", "lodash": "^4.17.21", "luxon": "^3.0.1", "moduleraid": "^5.1.0", "monaco-editor-webpack-plugin": "^7.0.1", "polished": "^4.2.2", "postcss": "^8.4.16", "postcss-loader": "^7.0.1", "postcss-preset-env": "^7.8.0", "prettier": "^2.7.1", "prism-react-renderer": "^1.3.5", "pronouns": "^1.3.3", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-monaco-editor": "^0.49.0", "react-virtual": "^2.10.4", "rimraf": "^3.0.2", "semver": "^7.3.7", "simple-plist": "^1.3.1", "string-replace-loader": "^3.1.0", "style-loader": "^3.3.1", "stylis": "^4.1.1", "title-case": "^3.0.3", "ts-key-enum": "^2.0.12", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "typescript": "^4.7.4", "use-debounce": "^8.0.3", "utility-types": "^3.10.0", "web-ext": "^7.2.0", "webextension-polyfill": "^0.10.0", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "xcode": "^3.0.1"}, "devDependencies": {"@types/jest": "^28.1.7", "jest": "^28.1.3", "ts-jest": "^28.0.8"}, "overrides": {"react": "^18.2.0", "react-dom": "^18.2.0"}}