:root {
  /* value set in `themeTweaks.ts */
  /* --btd-accent-color:; */
}

html .column-header-link:hover {
  color: rgb(143, 143, 143);
}

html.dark .column-header-link:hover {
  color: white;
}

html {
  /** Sidebar links/buttons */
  & .column-header-link,
  & .app-nav-link,
  & .app-nav-tab,
  & .app-search-fake {
    color: var(--btd-accent-color) !important;
  }
  & .app-nav-link:hover,
  & .app-nav-link.is-selected:hover,
  & .app-nav-tab:hover {
    color: white;
  }

  & .js-tweet-stat.is-actionable:hover .stat-word,
  & .txt-mute > a:hover,
  & .js-media-sensitive-overlay a,
  & [data-testid='trendLink'],
  & a.js-column-back,
  & .social-proof-names > a,
  & .activity-header .nbfc a[rel='list'],
  & article .account-bio a.link-complex,
  & article .account-bio a.url-ext,
  & .tweet-text a.link-complex,
  & .js-tweet a.link-complex:hover,
  & .js-quoted-tweet-text a.link-complex,
  & .tweet-text a.url-ext,
  & .js-quoted-tweet-text a.url-ext,
  & .prf-stats .js-action-url:hover,
  & .prf-stats .js-action-url:hover strong,
  & .prf .lst-profile a:hover i,
  & .prf .lst-profile a:hover span,
  & .color-twitter-blue,
  & .other-replies-link,
  & .other-replies-link:hover,
  & .facet-subtitle,
  & .compose .other-replies-link,
  & .compose .other-replies-link:hover,
  & .inline-reply .other-replies-link,
  & .inline-reply .other-replies-link:hover,
  & .med-flaglink,
  & .med-origlink,
  & .follow-btn .icon,
  & .follow-btn .Icon,
  & .conversation-more,
  & .compose-reply-tweet .tweet-body a {
    color: var(--btd-accent-color) !important;
  }

  /** General button colors */
  & button.btn-on-dark,
  & input[type='button'].btn-on-dark {
    color: var(--btd-accent-color);
    border-color: var(--btd-accent-color);
    background: transparent;
  }
  & button.btn-on-dark:hover,
  & input[type='button'].btn-on-dark:hover {
    color: var(--btd-accent-color);
  }
  & button.btn-on-dark:focus,
  & input[type='button'].btn-on-dark:focus {
    color: var(--btd-accent-color);
    border-color: var(--btd-accent-color);
    box-shadow: 0 0 0 2px #10171e, 0 0 0 4px var(--btd-accent-color) !important;
  }
  & .dropdown-menu .is-selected,
  & .lst-group .selected,
  & .lst .s-selected {
    background-color: var(--btd-accent-color) !important;
  }

  /** Primary button colors */
  & .Button.btn-fav.s-favorited,
  & .Button.btn-fav.s-favorited:visited,
  & .Button.Button--primary,
  & .Button.Button--primary:visited,
  & .ButtonGroup--primary > .Button,
  & .ButtonGroup--primary > .Button:visited,
  & .ButtonGroup--primary > .ButtonGroup > .Button,
  & .ButtonGroup--primary > .ButtonGroup > .Button:visited,
  & .ButtonGroup--primary > .ButtonGroup > button,
  & .ButtonGroup--primary > .ButtonGroup > button:visited,
  & .ButtonGroup--primary > .ButtonGroup > input:visited[type='button'],
  & .ButtonGroup--primary > .ButtonGroup > input[type='button'],
  & .ButtonGroup--primary > button,
  & .ButtonGroup--primary > button:visited,
  & .ButtonGroup--primary > input:visited[type='button'],
  & .ButtonGroup--primary > input[type='button'],
  & .s-following .follow-btn:hover .Button.following-text,
  & .s-following .follow-btn:hover .Button.following-text:visited,
  & .s-following .follow-btn:hover button.following-text,
  & .s-following .follow-btn:hover button.following-text:visited,
  & .s-following .follow-btn:hover input.following-text:visited[type='button'],
  & .s-following .follow-btn:hover input.following-text[type='button'],
  & button.btn-fav.s-favorited,
  & button.btn-fav.s-favorited:visited,
  & button.Button--primary,
  & button.Button--primary:visited,
  & input.btn-fav.s-favorited:visited[type='button'],
  & input.btn-fav.s-favorited[type='button'],
  & input.Button--primary:visited[type='button'],
  & input.Button--primary[type='button'] {
    background-color: var(--btd-accent-color) !important;
    border: none !important;
  }

  /** Hover styles */
  & .Button.btn-fav.s-favorited:hover,
  & .Button.Button--primary.is-hover,
  & .Button.Button--primary:hover,
  & .Button.is-hover.btn-fav.s-favorited,
  & .ButtonGroup--primary > .Button.is-hover,
  & .ButtonGroup--primary > .Button:hover,
  & .ButtonGroup--primary > .ButtonGroup > .Button.is-hover,
  & .ButtonGroup--primary > .ButtonGroup > .Button:hover,
  & .ButtonGroup--primary > .ButtonGroup > button.is-hover,
  & .ButtonGroup--primary > .ButtonGroup > button:hover,
  & .ButtonGroup--primary > .ButtonGroup > input.is-hover[type='button'],
  & .ButtonGroup--primary > .ButtonGroup > input:hover[type='button'],
  & .ButtonGroup--primary > button.is-hover,
  & .ButtonGroup--primary > button:hover,
  & .ButtonGroup--primary > input.is-hover[type='button'],
  & .ButtonGroup--primary > input:hover[type='button'],
  & .s-following .follow-btn:hover .Button.following-text:hover,
  & .s-following .follow-btn:hover .Button.is-hover.following-text,
  & .s-following .follow-btn:hover button.following-text:hover,
  & .s-following .follow-btn:hover button.is-hover.following-text,
  & .s-following .follow-btn:hover input.following-text:hover[type='button'],
  & .s-following .follow-btn:hover input.is-hover.following-text[type='button'],
  & button.btn-fav.s-favorited:hover,
  & button.Button--primary.is-hover,
  & button.Button--primary:hover,
  & button.is-hover.btn-fav.s-favorited,
  & input.btn-fav.s-favorited:hover[type='button'],
  & input.Button--primary.is-hover[type='button'],
  & input.Button--primary:hover[type='button'],
  & input.is-hover.btn-fav.s-favorited[type='button'],
  & .lst-group .selected a:hover {
    background-color: var(--btd-accent-color) !important;
    background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)) !important;
  }

  /** Column type picker */
  & .lst-launcher .is-disabled a:active i,
  & .lst-launcher .is-disabled a:focus i,
  & .lst-launcher .is-disabled a:hover i,
  & .lst-launcher .is-disabled a i {
    color: var(--btd-accent-color) !important;
  }
  & .lst-launcher a:active i,
  & .lst-launcher a:focus i,
  & .lst-launcher a:hover i {
    color: var(--btd-accent-color) !important;
    opacity: 0.9;
  }

  /** Search button. */
  & .app-search-fake {
    border-color: var(--btd-accent-color) !important;

    &:hover {
      border-color: white !important;
      color: white !important;
    }
  }

  /** Button text color */
  & button.btn-on-dark.btn-fav-unfav-text,
  & button.Button--danger.btn-on-dark,
  & input.btn-fav-unfav-text[type='button'].btn-on-dark,
  & input[type='button'].Button--danger.btn-on-dark {
    color: white;
  }

  /* Reset the color of the button to hide the drawer */
  &[class] .js-hide-drawer.Button.tweet-button {
    background-image: none !important;
    color: white;
    background: #2b7bb9 !important;
  }
  &[class].dark .js-hide-drawer.Button.tweet-button {
    background: #3d5466 !important;
  }
}
