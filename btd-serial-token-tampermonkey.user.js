// ==UserScript==
// @name         BetterTweetDeck Serial Token Enhancement
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Adds enhanced serial token functionality to BetterTweetDeck: uses 1,2,3,4 instead of 01,02,03,04 and skips serial for single media
// <AUTHOR>
// @match        *://tweetdeck.twitter.com/*
// @match        *://tweetdeck.dimden.dev/*
// @match        https://twitter.com/i/tweetdeck
// @match        https://x.com/i/tweetdeck
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // Wait for BetterTweetDeck to be ready
    function waitForBTD() {
        return new Promise((resolve) => {
            const checkBTD = () => {
                if (document.body.getAttribute('data-btd-ready') === 'true' && window.TD && window.BTD) {
                    resolve();
                } else {
                    setTimeout(checkBTD, 100);
                }
            };
            checkBTD();
        });
    }

    // Enhanced serial token functionality
    function enhanceSerialToken() {
        try {
            const TD = window.TD;
            if (!TD || !TD.mustaches) return;

            // Hook into BetterTweetDeck's mustache template system
            const originalMustacheSet = Object.getOwnPropertyDescriptor(TD.mustaches, "btd/download_filename_format.mustache");

            // Create a proxy to intercept template usage
            let currentTemplate = TD.mustaches["btd/download_filename_format.mustache"];

            Object.defineProperty(TD.mustaches, "btd/download_filename_format.mustache", {
                get: function() {
                    return currentTemplate;
                },
                set: function(value) {
                    currentTemplate = value;
                    console.log('BTD Serial Enhancement: Template updated:', value);
                },
                configurable: true,
                enumerable: true
            });

            // Hook into the Hogan template compilation if available
            if (window.Hogan && window.Hogan.compile) {
                const originalCompile = window.Hogan.compile;
                window.Hogan.compile = function(template) {
                    // Check if this looks like a filename template (support both {serial} and {{serial}})
                    if (typeof template === 'string' && (template.includes('{serial}') || template.includes('{{serial}}'))) {
                        // Create our enhanced template processor for both formats
                        let enhancedTemplate = template;

                        // Handle {serial} format
                        enhancedTemplate = enhancedTemplate.replace(/\{serial\}/g, '{{#btdEnhancedSerial}}{{btdEnhancedSerial}}{{/btdEnhancedSerial}}');

                        // Handle {{serial}} format
                        enhancedTemplate = enhancedTemplate.replace(/\{\{serial\}\}/g, '{{#btdEnhancedSerial}}{{btdEnhancedSerial}}{{/btdEnhancedSerial}}');

                        const compiled = originalCompile.call(this, enhancedTemplate);

                        // Override the render method
                        const originalRender = compiled.render;
                        compiled.render = function(context) {
                            // Add our enhanced serial logic to the context
                            const enhancedContext = { ...context };

                            // Determine if we should show serial
                            const serial = context.serial || 1;
                            const totalCount = getTotalMediaCount(context);

                            if (totalCount === 1) {
                                // Skip serial for single media
                                enhancedContext.btdEnhancedSerial = '';
                            } else {
                                // Use underscore + unpadded serial for multiple media
                                enhancedContext.btdEnhancedSerial = '_' + String(serial);
                            }

                            let result = originalRender.call(this, enhancedContext);

                            // Clean up any double dashes from empty serial
                            result = result.replace(/--+/g, '-').replace(/^-+|-+$/g, '');

                            return result;
                        };

                        return compiled;
                    }

                    return originalCompile.call(this, template);
                };
            }

            // Helper function to determine total media count
            function getTotalMediaCount(context) {
                // Try various ways to get the total media count
                if (context.totalMediaCount) return context.totalMediaCount;
                if (context.btd_total_count) return context.btd_total_count;

                // Try to get it from the current tweet context
                if (window.BTD && window.BTD.debug) {
                    try {
                        const tweetId = context.tweetId || context.id;
                        if (tweetId) {
                            const chirp = window.BTD.debug.getChirpFromKey(tweetId);
                            if (chirp && chirp.entities && chirp.entities.media) {
                                return chirp.entities.media.length;
                            }
                        }
                    } catch (e) {
                        // Ignore errors
                    }
                }

                return 1; // Default to 1 if we can't determine
            }

            console.log('BetterTweetDeck Serial Token Enhancement: Successfully hooked into filename generation');

        } catch (error) {
            console.error('BetterTweetDeck Serial Token Enhancement: Error setting up hooks:', error);
        }
    }

    // Alternative approach: Hook into download events
    function hookDownloadEvents() {
        // Listen for download-related events
        document.addEventListener('click', function(event) {
            const target = event.target;
            
            // Check if this is a media download action
            if (target && (target.classList.contains('js-media-image-link') || 
                         target.closest('.js-media-image-link') ||
                         target.classList.contains('media-img'))) {
                
                setTimeout(() => {
                    try {
                        // Try to modify any download filename generation
                        modifyDownloadFilename(target);
                    } catch (error) {
                        console.error('Error modifying download filename:', error);
                    }
                }, 100);
            }
        });
    }

    function modifyDownloadFilename(element) {
        // Find the tweet container
        const tweetElement = element.closest('[data-key]');
        if (!tweetElement) return;
        
        const tweetKey = tweetElement.getAttribute('data-key');
        if (!tweetKey || !window.BTD) return;
        
        // Try to get tweet data using BTD's debug functions
        const chirpData = window.BTD.debug?.getChirpFromKey?.(tweetKey);
        if (!chirpData) return;
        
        // Count media in the tweet
        const mediaCount = chirpData.entities?.media?.length || 0;
        
        // If there's only one media file, we might want to modify the filename
        if (mediaCount === 1) {
            // Store this information for potential use by filename generation
            element.setAttribute('data-btd-single-media', 'true');
        } else if (mediaCount > 1) {
            // Find the index of this media item
            const mediaElements = tweetElement.querySelectorAll('.js-media-image-link, .media-img');
            const mediaIndex = Array.from(mediaElements).indexOf(element) + 1;
            element.setAttribute('data-btd-media-index', mediaIndex.toString());
            element.setAttribute('data-btd-media-total', mediaCount.toString());
        }
    }

    // More direct approach: Hook into download filename generation
    function hookFilenameGeneration() {
        // Wait for the download functionality to be available
        const checkForDownload = setInterval(() => {
            if (window.TD && window.TD.mustaches && window.TD.mustaches["btd/download_filename_format.mustache"]) {
                clearInterval(checkForDownload);

                // Store the original template
                const originalTemplate = window.TD.mustaches["btd/download_filename_format.mustache"];

                // Create a custom template processor
                window.TD.mustaches["btd/download_filename_format.mustache"] = function(context) {
                    let template = originalTemplate;

                    if (typeof template === 'function') {
                        template = template.call(this, context);
                    }

                    if (typeof template === 'string' && (template.includes('{serial}') || template.includes('{{serial}}'))) {
                        // Get media count from context or current tweet
                        const totalCount = getMediaCountFromContext(context);
                        const serial = context.serial || 1;

                        if (totalCount === 1) {
                            // Remove serial for single media (both formats)
                            template = template.replace(/\{serial\}/g, '');
                            template = template.replace(/\{\{serial\}\}/g, '');
                        } else {
                            // Use underscore + unpadded serial for multiple media (both formats)
                            template = template.replace(/\{serial\}/g, '_' + String(serial));
                            template = template.replace(/\{\{serial\}\}/g, '_' + String(serial));
                        }

                        // Clean up double dashes
                        template = template.replace(/--+/g, '-').replace(/^-+|-+$/g, '');
                    }

                    return template;
                };

                console.log('BTD Serial Enhancement: Hooked into download filename generation');
            }
        }, 500);

        // Clear the interval after 30 seconds to avoid infinite checking
        setTimeout(() => clearInterval(checkForDownload), 30000);
    }

    function getMediaCountFromContext(context) {
        // Try to get media count from various sources
        if (context && context.totalMediaCount) return context.totalMediaCount;

        // Try to get from current tweet using BTD's debug functions
        if (window.BTD && window.BTD.debug && context) {
            try {
                const tweetId = context.tweetId || context.id;
                if (tweetId) {
                    const chirp = window.BTD.debug.getChirpFromKey(tweetId);
                    if (chirp && chirp.entities && chirp.entities.media) {
                        return chirp.entities.media.length;
                    }
                }
            } catch (e) {
                // Ignore errors
            }
        }

        return 1; // Default to 1
    }

    // Initialize the enhancement
    async function init() {
        console.log('BetterTweetDeck Serial Token Enhancement: Initializing...');

        // Wait for BetterTweetDeck to be ready
        await waitForBTD();

        console.log('BetterTweetDeck Serial Token Enhancement: BTD detected, setting up enhancements...');

        // Set up our enhancements
        enhanceSerialToken();
        hookDownloadEvents();
        hookFilenameGeneration();

        console.log('BetterTweetDeck Serial Token Enhancement: Setup complete!');
    }

    // Start initialization
    init().catch(error => {
        console.error('BetterTweetDeck Serial Token Enhancement: Initialization failed:', error);
    });

})();
