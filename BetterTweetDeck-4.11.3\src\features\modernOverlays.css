[btd-modern-overlays='true'] {
  /* Bigger native videos. */
  & .js-modal-panel .l-table .l-cell .js-media-native-video {
    max-height: 80vh !important;
    max-width: 80vw !important;
    height: 100% !important;
    width: 100% !important;
    object-fit: contain !important;
  }

  & .js-mediatable .js-modal-panel .med-tweet .js-tweet > :not(.tweet-footer),
  & .js-mediatable .js-modal-panel .med-embeditem .med-tray .media-preview-container ~ a {
    display: none;
  }

  & .js-mediatable .js-modal-panel .med-tweet .js-tweet .tweet-footer .tweet-actions {
    display: flex;
    justify-content: space-between;
  }

  & .js-mediatable .js-modal-panel .med-tweet .js-tweet .tweet-footer .tweet-actions > .tweet-action-item > .tweet-action {
    color: white !important;
  }

  & .js-mediatable .js-modal-panel {
    max-width: 80% !important;
  }

  & .js-mediatable .js-modal-panel .med-tweet .js-tweet .tweet-footer .tweet-actions .txt-size--12 {
    font-size: 13px !important;
    margin-left: 12px !important;
  }

  & .js-mediatable .js-modal-panel .med-tweet .js-tweet .tweet-footer .tweet-actions .icon {
    font-size: 24px !important;
  }

  & .js-mediatable .js-modal-panel .med-embeditem {
    bottom: 40px !important;
    top: -8px !important;
  }

  & .js-mediatable .mdl-media-next {
    right: 2vw;
  }

  & .js-mediatable .mdl-media-prev {
    left: 2vw;
  }
}
