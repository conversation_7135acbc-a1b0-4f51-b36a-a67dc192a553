/**
 * Firefox / standard CSS
 */
.btd-loaded.is-firefox[btd-scrollbar-style='hidden'] .column-scroller {
  scrollbar-width: none;
}

.btd-loaded.is-firefox[btd-scrollbar-style='slim'] .column-scroller,
.btd-loaded.is-firefox[btd-scrollbar-style='slim'] .js-app-columns-container.needs-scroll-bottom-offset.scroll-styled-h {
  scrollbar-width: thin;
}

/**
 * Webkit / Blink
 */

.btd-loaded:not(.is-firefox)[btd-scrollbar-style='hidden'] {
  & .js-column-scroller.scroll-styled-v::-webkit-scrollbar,
  & .js-dropdown-container.scroll-styled-v::-webkit-scrollbar {
    display: none;
  }

  & .js-app-columns-container.needs-scroll-bottom-offset.scroll-styled-h::-webkit-scrollbar {
    display: none;
  }
}

.btd-loaded:not(.is-firefox)[btd-scrollbar-style='slim'] {
  & .js-column-scroller.scroll-styled-v::-webkit-scrollbar,
  & .js-dropdown-container.scroll-styled-v::-webkit-scrollbar {
    width: 3px;
  }

  & .js-app-columns-container.scroll-styled-h::-webkit-scrollbar {
    height: 3px;
  }

  & .js-app-columns-container.needs-scroll-bottom-offset.scroll-styled-h {
    bottom: 0;
  }
}
