// ==UserScript==
// @name         Twitter/X Download Serial Numberer
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automatically adds serial numbers to downloaded Twitter/X images and videos
// <AUTHOR>
// @match        https://x.com/*
// @match        https://twitter.com/*
// @match        https://pbs.twimg.com/*
// @match        https://video.twimg.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function () {
  'use strict';

  // Track downloads per tweet/user to assign serial numbers
  const downloadCounters = new Map();

  // Custom filename format - you can modify this
  const FILENAME_FORMAT =
    '{{postedUser}}-{{fileName}}_{{serial}}-{{month}}-{{day}}-{{year}}-{{hours}}-{{minutes}}.{{fileExtension}}';

  // Extract tweet context from current page
  function getTweetContext() {
    const url = window.location.href;
    let tweetId = null;
    let username = null;

    // For TweetDeck, try to get context from the most recently clicked tweet
    const activeChirp = document.querySelector('[data-chirp-id]');
    if (activeChirp) {
      tweetId = activeChirp.getAttribute('data-chirp-id');

      // Try to get username from the tweet element
      const usernameElement =
        activeChirp.querySelector('[data-screen-name]') ||
        activeChirp.querySelector('.username') ||
        activeChirp.querySelector('.account-link');
      if (usernameElement) {
        username =
          usernameElement.getAttribute('data-screen-name') ||
          usernameElement.textContent.replace('@', '').trim();
      }
    }

    // Fallback: Extract from URL patterns
    if (!tweetId && url.includes('/status/')) {
      const match = url.match(/\/status\/(\d+)/);
      tweetId = match ? match[1] : null;
    }

    // Fallback: Try to get username from URL or page
    if (!username) {
      const userMatch = url.match(/\/([^\/]+)\/status/) || url.match(/x\.com\/([^\/]+)/);
      if (userMatch) {
        username = userMatch[1];
      }
    }

    // Final fallback: try to get from page elements
    if (!username) {
      const userElement =
        document.querySelector('[data-testid="User-Name"] a') ||
        document.querySelector('[data-screen-name]') ||
        document.querySelector('.username');
      if (userElement) {
        username =
          userElement.textContent.replace('@', '').trim() ||
          userElement.getAttribute('data-screen-name');
      }
    }

    return {tweetId: tweetId || 'unknown', username: username || 'unknown'};
  }

  // Generate filename with serial number
  function generateFilename(originalUrl, context) {
    const url = new URL(originalUrl);
    const pathname = url.pathname;
    const fileName = pathname.split('/').pop().split('.')[0];
    const fileExtension = pathname.split('.').pop() || 'jpg';

    // Create unique key for this download session
    const contextKey = `${context.username}-${context.tweetId || 'unknown'}`;

    // Get and increment serial number
    const currentSerial = downloadCounters.get(contextKey) || 0;
    const serial = currentSerial + 1;
    downloadCounters.set(contextKey, serial);

    // Get current date/time
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    // Replace template variables
    let filename = FILENAME_FORMAT.replace('{{postedUser}}', context.username)
      .replace('{{fileName}}', fileName)
      .replace('{{serial}}', String(serial).padStart(2, '0'))
      .replace('{{month}}', month)
      .replace('{{day}}', day)
      .replace('{{year}}', year)
      .replace('{{hours}}', hours)
      .replace('{{minutes}}', minutes)
      .replace('{{fileExtension}}', fileExtension);

    return filename;
  }

  // Override the saveAs function used by BetterTweetDeck
  function interceptDownloads() {
    // Wait for saveAs to be available (from file-saver library)
    function waitForSaveAs() {
      if (window.saveAs) {
        const originalSaveAs = window.saveAs;
        window.saveAs = function (blob, filename) {
          console.log(`[Serial Numberer] Intercepted saveAs: ${filename}`);

          // Check if this is a Twitter media file
          if (
            filename &&
            (filename.includes('.jpg') ||
              filename.includes('.png') ||
              filename.includes('.mp4') ||
              filename.includes('.gif'))
          ) {
            const context = getTweetContext();

            // Extract original filename parts
            const parts = filename.split('.');
            const extension = parts.pop();
            const baseName = parts.join('.');

            // Create new filename with serial
            const contextKey = `${context.username}-${context.tweetId || 'unknown'}`;
            const currentSerial = downloadCounters.get(contextKey) || 0;
            const serial = currentSerial + 1;
            downloadCounters.set(contextKey, serial);

            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            const newFilename = FILENAME_FORMAT.replace('{{postedUser}}', context.username)
              .replace('{{fileName}}', baseName)
              .replace('{{serial}}', String(serial).padStart(2, '0'))
              .replace('{{month}}', month)
              .replace('{{day}}', day)
              .replace('{{year}}', year)
              .replace('{{hours}}', hours)
              .replace('{{minutes}}', minutes)
              .replace('{{fileExtension}}', extension);

            console.log(`[Serial Numberer] Renaming: ${filename} → ${newFilename}`);
            return originalSaveAs.call(this, blob, newFilename);
          }

          return originalSaveAs.call(this, blob, filename);
        };
        console.log('[Serial Numberer] saveAs intercepted successfully');
      } else {
        // Try again in 100ms
        setTimeout(waitForSaveAs, 100);
      }
    }

    waitForSaveAs();

    // Also intercept if saveAs gets loaded later
    const originalDefine = window.define;
    if (originalDefine) {
      window.define = function (name, deps, factory) {
        const result = originalDefine.apply(this, arguments);
        if (name && name.includes('file-saver')) {
          setTimeout(waitForSaveAs, 100);
        }
        return result;
      };
    }
  }

  // Monitor for download events
  function monitorDownloads() {
    // Watch for download links being created
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) {
            // Element node
            // Check if it's a download link
            if (node.tagName === 'A' && node.download && node.href) {
              const context = getTweetContext();
              if (node.href.includes('pbs.twimg.com') || node.href.includes('video.twimg.com')) {
                const newFilename = generateFilename(node.href, context);
                console.log(
                  `[Serial Numberer] Renaming download: ${node.download} → ${newFilename}`
                );
                node.download = newFilename;
              }
            }

            // Check child elements
            const downloadLinks = node.querySelectorAll && node.querySelectorAll('a[download]');
            if (downloadLinks) {
              downloadLinks.forEach((link) => {
                const context = getTweetContext();
                if (link.href.includes('pbs.twimg.com') || link.href.includes('video.twimg.com')) {
                  const newFilename = generateFilename(link.href, context);
                  console.log(
                    `[Serial Numberer] Renaming download: ${link.download} → ${newFilename}`
                  );
                  link.download = newFilename;
                }
              });
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  // Reset counters when navigating to new tweet
  let currentUrl = location.href;
  function monitorNavigation() {
    setInterval(() => {
      if (location.href !== currentUrl) {
        const oldUrl = currentUrl;
        currentUrl = location.href;

        // Reset counters if we moved to a different tweet
        const oldTweetId = oldUrl.match(/\/status\/(\d+)/)?.[1];
        const newTweetId = currentUrl.match(/\/status\/(\d+)/)?.[1];

        if (oldTweetId !== newTweetId) {
          console.log('[Serial Numberer] Navigation detected, resetting counters');
          downloadCounters.clear();
        }
      }
    }, 1000);
  }

  // Initialize
  function init() {
    console.log('[Serial Numberer] Initializing download interceptor...');
    interceptDownloads();

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        monitorDownloads();
        monitorNavigation();
      });
    } else {
      monitorDownloads();
      monitorNavigation();
    }
  }

  init();

  // Expose function to manually reset counters
  window.resetDownloadCounters = () => {
    downloadCounters.clear();
    console.log('[Serial Numberer] Download counters reset');
  };

  // Expose function to change filename format
  window.setFilenameFormat = (format) => {
    FILENAME_FORMAT = format;
    console.log('[Serial Numberer] Filename format updated:', format);
  };
})();
