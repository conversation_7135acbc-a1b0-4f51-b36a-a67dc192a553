<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="26" height="27">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.81103 0.181824C1.25842 0.181824 0 1.44018 0 2.99244V19.2393C0 20.7917 1.25854 22.0499 2.81103 22.0499H9.05065L13.028 26.0273L17.0053 22.0499H23.189C24.7416 22.0499 26 20.7916 26 19.2393V2.99244C26 1.44008 24.7415 0.181824 23.189 0.181824H2.81103Z" fill="#C4C4C4"/>
</mask>
<g mask="url(#mask0)">
<rect x="-1.25" y="20.625" width="31.3125" height="7.3125" fill="#333333"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-2 15H28V26H-2V15Z" fill="#80179A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-2 10H28V16H-2V10Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-2 5H28V11H-2V5Z" fill="#747474"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M-2 0H28V6H-2V0Z" fill="#333333"/>
</g>
</g>
<defs>
<clipPath id="clip0">
<rect width="26" height="26" fill="white"/>
</clipPath>
</defs>
</svg>
