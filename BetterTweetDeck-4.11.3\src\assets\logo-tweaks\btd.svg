<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="26" height="27">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.81103 0.181824C1.25842 0.181824 0 1.44018 0 2.99244V19.2393C0 20.7917 1.25854 22.0499 2.81103 22.0499H9.05065L13.028 26.0273L17.0053 22.0499H23.189C24.7416 22.0499 26 20.7916 26 19.2393V2.99244C26 1.44008 24.7415 0.181824 23.189 0.181824H2.81103Z" fill="#C4C4C4"/>
</mask>
<g mask="url(#mask0)">
<rect x="-1" width="28" height="26" fill="url(#paint0_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.3971 12.357H18V9.61566H14.3971V6H11.6029V9.61566H8V12.357H11.6029V16H14.3971V12.357Z" fill="black" style="mix-blend-mode:soft-light"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.3971 12.357H18V9.61566H14.3971V6H11.6029V9.61566H8V12.357H11.6029V16H14.3971V12.357Z" fill="black" fill-opacity="0.695652"/>
</g>
</g>
<defs>
<linearGradient id="paint0_linear" x1="20.3718" y1="13.6906" x2="20.3718" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#00A0FB"/>
<stop offset="1" stop-color="#00CBFC"/>
</linearGradient>
<clipPath id="clip0">
<rect width="26" height="26" fill="white"/>
</clipPath>
</defs>
</svg>
