.btd-loaded[btd-badges-top-avatar='true'] {
  & .fullname-badged {
    padding-right: 0;
  }
  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img,
  & .js-stream-item.btd-mini-badge > .js-stream-item-content > .activity-header > .nbfc,
  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img,
  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img {
    position: relative;
  }

  & .js-stream-item.btd-mini-badge > .js-stream-item-content > .activity-header > .nbfc::before,
  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before,
  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    content: '';
    line-height: 1;
    position: absolute;
    width: 1em;
    height: 1em;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-size: 16px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    z-index: 9;
  }

  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img {
    position: absolute;

    /* Helps with the positioning of the pseudo-element */
    height: 36px;
    width: 36px;
  }

  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before {
    bottom: -3px;
    right: -3px;
  }

  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    bottom: -3px;
    right: -3px;
  }

  /*
 small avatar on activities ("Bob liked", "Alice retweeted", "Damien added you to a list", etc)
 */
  & .js-stream-item.btd-mini-badge > .js-stream-item-content > .activity-header > .nbfc {
    position: relative;
  }

  & .js-stream-item.btd-mini-badge > .js-stream-item-content > .activity-header > .nbfc::before {
    top: 8px;
    left: 10px;
    transform-origin: bottom right;
    transform: scale(0.7);
  }

  /* Hide the vanilla badge in columns */
  & .stream-item .tweet:not(.txt-mute) > header .sprite-verified-mini,
  & .js-stream-item .account-link .fullname::before {
    display: none;
  }

  & .js-stream-item.btd-mini-badge.btd-verified-badge > .js-stream-item-content > .activity-header > .nbfc::before,
  & .js-stream-item.btd-badge.btd-verified-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before,
  & .js-stream-item.btd-badge.btd-verified-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge.btd-verified-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    background-image: url('../assets/verified-icon.svg');
  }

  & .js-stream-item.btd-mini-badge.btd-mutual-badge.btd-mutual-heart > .js-stream-item-content > .activity-header > .nbfc::before,
  & .js-stream-item.btd-badge.btd-mutual-heart.btd-mutual-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before,
  & .js-stream-item.btd-badge.btd-mutual-heart.btd-mutual-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge.btd-mutual-heart.btd-mutual-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    background-image: url('../assets/heart-icon.svg');
  }

  & .js-stream-item.btd-mini-badge.btd-mutual-badge.btd-mutual-arrows > .js-stream-item-content > .activity-header > .nbfc::before,
  & .js-stream-item.btd-badge.btd-mutual-arrows.btd-mutual-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before,
  & .js-stream-item.btd-badge.btd-mutual-arrows.btd-mutual-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge.btd-mutual-arrows.btd-mutual-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    background-image: url('../assets/mutual-icon.svg');
  }

  & .js-stream-item.btd-mini-badge.btd-translator-badge > .js-stream-item-content > .activity-header > .nbfc::before,
  & .js-stream-item.btd-badge.btd-translator-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before,
  & .js-stream-item.btd-badge.btd-translator-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge.btd-translator-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    background-image: url('../assets/translator-icon.svg');
  }

  & .js-stream-item.btd-mini-badge.btd-verified-business-badge > .js-stream-item-content > .activity-header > .nbfc::before,
  & .js-stream-item.btd-badge.btd-verified-business-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before,
  & .js-stream-item.btd-badge.btd-verified-business-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge.btd-verified-business-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    background-image: url('../assets/business-verified.svg');
  }

  & .js-stream-item.btd-mini-badge.btd-verified-government-badge > .js-stream-item-content > .activity-header > .nbfc::before,
  & .js-stream-item.btd-badge.btd-verified-government-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet > .tweet-header .item-img.tweet-img::before,
  & .js-stream-item.btd-badge.btd-verified-government-badge:not(.btd-mini-badge) > .js-stream-item-content > .tweet-detail .account-summary .item-img::before,
  & .js-stream-item.btd-badge.btd-verified-government-badge:not(.btd-mini-badge) > .js-stream-item-content > .account-summary .item-img::before {
    background-image: url('../assets/gov-verified.svg');
  }
}
